# Proposed loss for face anti spoofing

This documents aims to summarize the depth loss proposed by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> in their paper: *Exploiting temporal and depth information for multi-frame face anti-spoofing*

```bash
                                   ----------
A single (256x256x3) RGB Image -> |   CDCN   | -> A 32x32 Depth Map
                                   ----------
```

Depth loss consists of 2 parts:
1. Squared Euclidean norm loss between predicted depth map and ground truth depth map

![MSE loss](assets/mse-loss.png)

2. Contrastive depth loss  
![Contrastive loss](assets/contrastive-loss.png)
