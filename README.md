# Apply Central Difference Convolutional Network (CDCN) for Face Anti Spoofing

## Installation

```bash
virtualenv -p python3 venv
source venv/bin/activate
pip install -r requirements.txt
```


## Dataset preparation


## Reference

[1] <PERSON>, Zito<PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>. Searching Central Difference Convolutional Networks for Face Anti-Spoofing, doi: https://arxiv.org/pdf/2003.04092v1.pdf  
[2] Central Difference Convolutional Networks, doi: https://github.com/ZitongYu/CDCN  
[3] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Exploiting temporal and depth information for multi-frame face anti-spoofing, doi: https://arxiv.org/pdf/1811.05118.pdf
